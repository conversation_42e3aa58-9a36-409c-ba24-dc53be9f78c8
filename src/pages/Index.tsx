import { useState, useMemo } from "react";
import { Users, Target, Calendar, TrendingUp, UserCheck, UserX, RotateCcw, XCircle, CheckCircle, LogOut } from "lucide-react";
import { isWithinInterval, parseISO, startOfWeek, endOfWeek, subDays } from "date-fns";

// Components
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { AppointmentsTable } from "@/components/dashboard/AppointmentsTable";
import { AppointmentBreakdown } from "@/components/dashboard/AppointmentBreakdown";
import { TimeSeriesChart } from "@/components/dashboard/TimeSeriesChart";
import { MetricCard } from "@/components/ui/metric-card";
import { Button } from "@/components/ui/button";

// Data and hooks
import { Appointment } from "@/data/mockData";
import { useClientMetrics } from "@/hooks/useClientMetrics";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const { user, profile, signOut } = useAuth();
  const { appointments, loading, error } = useClientMetrics();
  const today = new Date();
  const [filters, setFilters] = useState({
    dateRange: {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 })
    },
    selectedClosers: [] as string[],
    selectedSetters: [] as string[]
  });

  const [visibleMetrics, setVisibleMetrics] = useState({
    totalAppointments: true,
    totalSits: true,
    totalCloses: true,
    noShows: true,
    rescheduled: true,
    notInterested: true,
    disqualified: true,
    appointmentBreakdown: true,
    detailedTable: true,
    performanceChart: true
  });

  // Extract unique closers and setters
  const closers = useMemo(() =>
    [...new Set(appointments.map(apt => apt.closer_name))].filter(Boolean).sort(),
    [appointments]
  );

  const setters = useMemo(() =>
    [...new Set(appointments.map(apt => apt.setter_name))].filter(Boolean).sort(),
    [appointments]
  );

  // Filter appointments based on current filters
  const filteredAppointments = useMemo(() => {
    return appointments.filter(appointment => {
      // Date range filter
      try {
        const appointmentDate = parseISO(appointment.booked_for);
        const isInDateRange = isWithinInterval(appointmentDate, {
          start: filters.dateRange.start,
          end: filters.dateRange.end
        });

        // Closer filter
        const closerMatch = filters.selectedClosers.length === 0 ||
          filters.selectedClosers.includes(appointment.closer_name);

        // Setter filter
        const setterMatch = filters.selectedSetters.length === 0 ||
          filters.selectedSetters.includes(appointment.setter_name);

        return isInDateRange && closerMatch && setterMatch;
      } catch (error) {
        // Skip appointments with invalid dates
        return false;
      }
    });
  }, [appointments, filters]);

  // Calculate KPIs with previous period comparison
  const metrics = useMemo(() => {
    const total = filteredAppointments.length;
    const sits = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Sat').length;
    const closes = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Closed').length;
    const noShows = filteredAppointments.filter(apt => apt.confirmation_disposition === 'No Show').length;
    const rescheduled = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length;
    const notInterested = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length;
    const disqualified = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length;
    
    // Calculate percentage change vs previous period
    const currentPeriodDays = Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const previousStart = subDays(filters.dateRange.start, currentPeriodDays);
    const previousEnd = subDays(filters.dateRange.end, currentPeriodDays);
    
    const previousAppointments = mockAppointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.booked_for);
      return isWithinInterval(appointmentDate, {
        start: previousStart,
        end: previousEnd
      });
    });
    
    const previousTotal = previousAppointments.length;
    const percentageChange = previousTotal > 0 ? (((total - previousTotal) / previousTotal) * 100) : 0;
    
    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      percentageChange: percentageChange.toFixed(1),
      percentageChangeNumeric: percentageChange,
      noShowsPercentage: total > 0 ? ((noShows / total) * 100).toFixed(1) : '0.0',
      rescheduledPercentage: total > 0 ? ((rescheduled / total) * 100).toFixed(1) : '0.0',
      notInterestedPercentage: total > 0 ? ((notInterested / total) * 100).toFixed(1) : '0.0',
      disqualifiedPercentage: total > 0 ? ((disqualified / total) * 100).toFixed(1) : '0.0',
    };
  }, [filteredAppointments, filters.dateRange]);

  const toggleMetric = (metric: string) => {
    setVisibleMetrics(prev => ({
      ...prev,
      [metric]: !prev[metric]
    }));
  };

  const handleSignOut = async () => {
    await signOut();
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-dashboard-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-dashboard-bg flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-foreground mb-2">Error Loading Data</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <DashboardHeader
        visibleMetrics={visibleMetrics}
        onToggleMetric={toggleMetric}
        user={user}
        profile={profile}
        onSignOut={handleSignOut}
      />
      
      <main className="container mx-auto px-6 py-6">
        {/* Filters */}
        <FilterBar
          closers={closers}
          setters={setters}
          onFiltersChange={setFilters}
        />

        {/* Performance Over Time Chart */}
        {visibleMetrics.performanceChart && (
          <TimeSeriesChart appointments={filteredAppointments} dateRange={filters.dateRange} />
        )}

        {/* KPI Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {visibleMetrics.totalAppointments && (
            <MetricCard
              title="Total Appointments"
              value={metrics.total}
              subtitle={
                <span>
                  All appointments in selected period<br />
                  <span className="text-blue-600 font-semibold">
                    Change: {metrics.percentageChangeNumeric > 0 ? '+' : ''}{metrics.percentageChange}% vs previous period
                  </span>
                </span>
              }
              icon={Calendar}
              variant="info"
            />
          )}
          {visibleMetrics.totalSits && (
            <MetricCard
              title="Total Sits"
              value={metrics.sits}
              subtitle={
                <span>
                  Successfully sat appointments<br />
                  <span className="text-green-600 font-semibold">
                    Show Rate: {metrics.total > 0 ? ((metrics.sits / metrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={UserCheck}
              variant="success"
            />
          )}
          {visibleMetrics.totalCloses && (
            <MetricCard
              title="Total Closes"
              value={metrics.closes}
              subtitle={
                <span>
                  Successfully closed deals<br />
                  <span className="text-green-600 font-semibold">
                    Close Rate: {metrics.total > 0 ? ((metrics.closes / metrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={CheckCircle}
              variant="success"
            />
          )}
        </div>

        {/* Disposition Analysis Section */}
        {(visibleMetrics.noShows || visibleMetrics.rescheduled || visibleMetrics.notInterested || visibleMetrics.disqualified) && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-foreground mb-4">Disposition Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {visibleMetrics.noShows && (
                <MetricCard
                  title="No Show"
                  value={metrics.noShows}
                  subtitle={`${metrics.noShowsPercentage}% of total appointments`}
                  icon={XCircle}
                  variant="default"
                />
              )}
              {visibleMetrics.rescheduled && (
                <MetricCard
                  title="Rescheduled"
                  value={metrics.rescheduled}
                  subtitle={`${metrics.rescheduledPercentage}% of total appointments`}
                  icon={RotateCcw}
                  variant="warning"
                />
              )}
              {visibleMetrics.notInterested && (
                <MetricCard
                  title="Not Interested"
                  value={metrics.notInterested}
                  subtitle={`${metrics.notInterestedPercentage}% of total appointments`}
                  icon={UserX}
                  variant="default"
                />
              )}
              {visibleMetrics.disqualified && (
                <MetricCard
                  title="Disqualified"
                  value={metrics.disqualified}
                  subtitle={`${metrics.disqualifiedPercentage}% of total appointments`}
                  icon={Target}
                  variant="default"
                />
              )}
            </div>
          </div>
        )}

        {/* Appointment Breakdown Section */}
        {visibleMetrics.appointmentBreakdown && (
          <AppointmentBreakdown appointments={filteredAppointments} />
        )}

        {/* Detailed Appointments Table */}
        {visibleMetrics.detailedTable && (
          <AppointmentsTable appointments={filteredAppointments} />
        )}
      </main>
    </div>
  );
};

export default Index;
